// 日志管理工具
// 解决微信小程序日志文件存储超限问题

const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

const LOG_LEVEL_NAMES = ['DEBUG', 'INFO', 'WARN', 'ERROR'];

class Logger {
  constructor() {
    this.config = {
      // 是否启用日志
      enabled: true,
      // 当前日志级别
      level: LOG_LEVELS.INFO,
      // 最大日志条数（防止内存占用过大）
      maxLogs: 50,
      // 是否自动清理存储
      autoClean: true,
      // 存储清理阈值（MB）
      cleanThreshold: 8
    };
    
    this.logs = [];
    this.init();
  }

  init() {
    // 检查并清理存储空间
    if (this.config.autoClean) {
      this.checkAndCleanStorage();
    }
    
    // 重写 console 方法以控制日志输出
    this.overrideConsole();
  }

  // 检查并清理存储空间
  checkAndCleanStorage() {
    try {
      wx.getStorageInfo({
        success: (res) => {
          const currentSize = res.currentSize;
          const limitSize = res.limitSize;
          const usagePercent = (currentSize / limitSize) * 100;
          
          console.log(`存储使用情况: ${currentSize}KB / ${limitSize}KB (${usagePercent.toFixed(1)}%)`);
          
          // 如果存储使用超过阈值，清理数据
          if (usagePercent > this.config.cleanThreshold * 10) { // 转换为百分比
            this.cleanStorage();
          }
        },
        fail: (err) => {
          console.error('获取存储信息失败:', err);
        }
      });
    } catch (e) {
      console.error('检查存储空间失败:', e);
    }
  }

  // 清理存储空间
  cleanStorage() {
    try {
      // 清理可能的大数据缓存
      const keysToClean = [
        'aiRecommendResult',
        'logs',
        'tempData',
        'cache'
      ];
      
      keysToClean.forEach(key => {
        try {
          wx.removeStorageSync(key);
        } catch (e) {
          // 忽略删除失败的情况
        }
      });
      
      console.log('存储空间清理完成');
    } catch (e) {
      console.error('清理存储空间失败:', e);
    }
  }

  // 重写 console 方法
  overrideConsole() {
    const originalConsole = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error
    };

    // 重写 console.log
    console.log = (...args) => {
      this.log(LOG_LEVELS.INFO, ...args);
      // 只在开发环境或调试模式下输出到原始 console
      if (this.shouldOutputToConsole()) {
        originalConsole.log(...args);
      }
    };

    // 重写 console.info
    console.info = (...args) => {
      this.log(LOG_LEVELS.INFO, ...args);
      if (this.shouldOutputToConsole()) {
        originalConsole.info(...args);
      }
    };

    // 重写 console.warn
    console.warn = (...args) => {
      this.log(LOG_LEVELS.WARN, ...args);
      if (this.shouldOutputToConsole()) {
        originalConsole.warn(...args);
      }
    };

    // 重写 console.error
    console.error = (...args) => {
      this.log(LOG_LEVELS.ERROR, ...args);
      if (this.shouldOutputToConsole()) {
        originalConsole.error(...args);
      }
    };
  }

  // 判断是否应该输出到原始 console
  shouldOutputToConsole() {
    // 在开发环境下输出，生产环境下不输出
    try {
      const accountInfo = wx.getAccountInfoSync();
      return accountInfo.miniProgram.envVersion === 'develop' || 
             accountInfo.miniProgram.envVersion === 'trial';
    } catch (e) {
      return true; // 如果获取失败，默认输出
    }
  }

  // 核心日志方法
  log(level, ...args) {
    if (!this.config.enabled || level < this.config.level) {
      return;
    }

    const timestamp = new Date().toISOString();
    const levelName = LOG_LEVEL_NAMES[level];
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ');

    const logEntry = {
      timestamp,
      level: levelName,
      message
    };

    // 添加到内存日志
    this.logs.push(logEntry);

    // 限制内存中的日志数量
    if (this.logs.length > this.config.maxLogs) {
      this.logs.shift(); // 移除最旧的日志
    }
  }

  // 公共日志方法
  debug(...args) {
    this.log(LOG_LEVELS.DEBUG, ...args);
  }

  info(...args) {
    this.log(LOG_LEVELS.INFO, ...args);
  }

  warn(...args) {
    this.log(LOG_LEVELS.WARN, ...args);
  }

  error(...args) {
    this.log(LOG_LEVELS.ERROR, ...args);
  }

  // 获取日志
  getLogs() {
    return this.logs;
  }

  // 清空日志
  clearLogs() {
    this.logs = [];
  }

  // 设置日志级别
  setLevel(level) {
    this.config.level = level;
  }

  // 启用/禁用日志
  setEnabled(enabled) {
    this.config.enabled = enabled;
  }
}

// 创建全局日志实例
const logger = new Logger();

// 导出日志工具
module.exports = {
  logger,
  LOG_LEVELS
};
